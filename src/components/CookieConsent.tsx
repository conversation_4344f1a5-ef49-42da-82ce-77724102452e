'use client';

import { useState, useEffect } from 'react';

const CookieConsent = () => {
  const [showIcon, setShowIcon] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  useEffect(() => {
    // Check if user has already made a choice about cookies
    const cookieConsent = localStorage.getItem('cookieConsent');
    if (!cookieConsent) {
      setShowIcon(true);
    }
  }, []);

  const acceptCookies = () => {
    localStorage.setItem('cookieConsent', 'accepted');
    setShowIcon(false);
    setShowModal(false);
  };

  const declineCookies = () => {
    localStorage.setItem('cookieConsent', 'declined');
    setShowIcon(false);
    setShowModal(false);
  };

  const openModal = () => {
    setShowModal(true);
    setShowTooltip(false);
  };

  const closeModal = () => {
    setShowModal(false);
  };

  if (!showIcon) return null;

  return (
    <>
      {/* Cookie Icon with Tooltip */}
      <div className="fixed bottom-4 left-4 z-50">
        <div className="relative">
          <button
            onClick={openModal}
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
            className="w-12 h-12 bg-light-green rounded-full flex items-center justify-center shadow-lg hover:bg-dark-green transition-colors cursor-pointer"
          >
            <svg
              className="w-6 h-6 text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
              <circle cx="9" cy="9" r="1"/>
              <circle cx="15" cy="9" r="1"/>
              <circle cx="12" cy="15" r="1"/>
              <circle cx="6" cy="12" r="1"/>
              <circle cx="18" cy="12" r="1"/>
            </svg>
          </button>

          {/* Tooltip */}
          {showTooltip && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap">
              Cookie Settings
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
            </div>
          )}
        </div>
      </div>

      {/* Modal Overlay */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Black Overlay */}
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={closeModal}
          ></div>

          {/* Modal Content */}
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
            {/* Close Button */}
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Modal Header */}
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-light-green rounded-full flex items-center justify-center">
                <svg
                  className="w-6 h-6 text-white"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                  <circle cx="9" cy="9" r="1"/>
                  <circle cx="15" cy="9" r="1"/>
                  <circle cx="12" cy="15" r="1"/>
                  <circle cx="6" cy="12" r="1"/>
                  <circle cx="18" cy="12" r="1"/>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Cookie Settings
              </h3>
            </div>

            {/* Modal Body */}
            <div className="mb-6">
              <p className="text-sm text-gray-600 mb-4">
                We use cookies to enhance your browsing experience, serve personalized content, and analyze our traffic.
                By clicking "Accept All", you consent to our use of cookies in accordance with Qatar's data protection regulations.
              </p>

              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Essential Cookies</h4>
                    <p className="text-xs text-gray-600">Required for website functionality</p>
                  </div>
                  <div className="text-xs text-gray-500">Always Active</div>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Analytics Cookies</h4>
                    <p className="text-xs text-gray-600">Help us improve our website</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-light-green"></div>
                  </label>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex space-x-3">
              <button
                onClick={acceptCookies}
                className="flex-1 px-4 py-2 bg-light-green text-white text-sm font-medium rounded hover:bg-dark-green transition-colors"
              >
                Accept All
              </button>
              <button
                onClick={declineCookies}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded hover:bg-gray-50 transition-colors"
              >
                Decline All
              </button>
            </div>

            {/* Learn More Link */}
            <div className="mt-3 text-center">
              <a
                href="/cookies-policy"
                className="text-xs text-light-green hover:text-dark-green underline"
                onClick={closeModal}
              >
                Learn more about our cookie policy
              </a>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CookieConsent;
