'use client';

import { useState, useEffect } from 'react';

const CookieConsent = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if user has already accepted cookies
    const hasAccepted = localStorage.getItem('cookieConsent');
    if (!hasAccepted) {
      setIsVisible(true);
    }
  }, []);

  const acceptCookies = () => {
    localStorage.setItem('cookieConsent', 'accepted');
    setIsVisible(false);
  };

  const declineCookies = () => {
    localStorage.setItem('cookieConsent', 'declined');
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 left-4 z-50 max-w-sm bg-white rounded-lg shadow-lg border border-gray-200 p-4">
      <div className="flex items-start space-x-3">
        {/* <PERSON><PERSON> */}
        <div className="flex-shrink-0 w-10 h-10 bg-light-green rounded-full flex items-center justify-center">
          <svg 
            className="w-6 h-6 text-white" 
            fill="currentColor" 
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
            <circle cx="9" cy="9" r="1"/>
            <circle cx="15" cy="9" r="1"/>
            <circle cx="12" cy="15" r="1"/>
            <circle cx="6" cy="12" r="1"/>
            <circle cx="18" cy="12" r="1"/>
          </svg>
        </div>
        
        {/* Content */}
        <div className="flex-1">
          <h3 className="text-sm font-semibold text-gray-900 mb-1">
            We use cookies
          </h3>
          <p className="text-xs text-gray-600 mb-3">
            We use cookies to enhance your browsing experience, serve personalized content, and analyze our traffic. 
            By clicking "Accept All", you consent to our use of cookies in accordance with Qatar's data protection regulations.
          </p>
          
          {/* Buttons */}
          <div className="flex space-x-2">
            <button
              onClick={acceptCookies}
              className="px-3 py-1.5 bg-light-green text-white text-xs font-medium rounded hover:bg-dark-green transition-colors"
            >
              Accept All
            </button>
            <button
              onClick={declineCookies}
              className="px-3 py-1.5 border border-gray-300 text-gray-700 text-xs font-medium rounded hover:bg-gray-50 transition-colors"
            >
              Decline
            </button>
          </div>
          
          {/* Learn More Link */}
          <div className="mt-2">
            <a
              href="/cookies-policy"
              className="text-xs text-light-green hover:text-dark-green underline"
            >
              Learn more about our cookie policy
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
